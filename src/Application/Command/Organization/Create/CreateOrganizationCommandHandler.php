<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Create;

use App\Application\Shared\Organization\OrganizationResponse;
use App\Application\Shared\Error\ErrorResponse;
use App\Domain\Model\Error\ValidatorInterface;
use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\Organization\OrganizationValidatorInterface;

final readonly class CreateOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository,
        private readonly OrganizationValidatorInterface $validator,
    ) {}

    public function __invoke(CreateOrganizationCommand $command): ErrorResponse|OrganizationResponse
    {
        $errors = $this->validator->validate($command->toArray(), ValidatorInterface::GROUP_CREATE);
        if ($errors->hasErrors()) {
            return ErrorResponse::fromConstraintViolationList($errors);
        }

        $uniqueName = $this->validator->validateNameUniqueness($command->getName());
        if (null !== $uniqueName) {
            return ErrorResponse::fromError($uniqueName->getMessage(), $uniqueName->getPath());
        }

        $organization = new Organization(
            $command->getName(),
            $command->getEmail(),
            $command->getPhone(),
            $command->getAddress()
        );

        $this->organizationRepository->save($organization);
        return OrganizationResponse::fromOrganization($organization);
    }
}
