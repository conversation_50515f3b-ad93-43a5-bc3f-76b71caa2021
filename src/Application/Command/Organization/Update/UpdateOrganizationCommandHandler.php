<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Update;

use App\Application\Shared\Organization\OrganizationResponse;
use App\Application\Shared\Error\ErrorResponse;
use App\Domain\Model\Error\ValidatorInterface;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\Organization\OrganizationValidatorInterface;

final readonly class UpdateOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository,
        private readonly OrganizationValidatorInterface $validator,
    ) {}

    public function __invoke(UpdateOrganizationCommand $command): ErrorResponse|OrganizationResponse
    {
        // First check if organization exists
        $organization = $this->organizationRepository->find($command->getId());
        if (null === $organization) {
            return ErrorResponse::notFound('Organization not found');
        }

        // Validate the update data
        $errors = $this->validator->validate($command->toArray(), ValidatorInterface::GROUP_UPDATE);
        if ($errors->hasErrors()) {
            return ErrorResponse::fromConstraintViolationList($errors);
        }

        // Check name uniqueness (excluding current organization)
        $uniqueName = $this->validator->validateNameUniqueness($command->getName(), $command->getId());
        if (null !== $uniqueName) {
            return ErrorResponse::fromError($uniqueName->getMessage(), $uniqueName->getPath());
        }

        // Update the organization
        $organization->update(
            $command->getName(),
            $command->getEmail(),
            $command->getPhone(),
            $command->getAddress()
        );

        $this->organizationRepository->save($organization);

        return OrganizationResponse::fromOrganization($organization);
    }
}
