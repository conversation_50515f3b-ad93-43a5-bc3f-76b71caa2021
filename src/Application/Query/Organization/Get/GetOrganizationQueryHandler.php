<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\Get;

use App\Application\Shared\Error\ErrorResponse;
use App\Application\Shared\Organization\OrganizationResponse;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class GetOrganizationQueryHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $repository
    ) {}

    public function __invoke(GetOrganizationQuery $query): ErrorResponse|OrganizationResponse
    {
        $organization = $this->repository->find($query->getId());

        if (null === $organization) {
            return ErrorResponse::notFound('Organization not found');
        }

        return OrganizationResponse::fromOrganization($organization);
    }
}
