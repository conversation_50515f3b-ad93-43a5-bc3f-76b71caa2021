<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\Organization\OrganizationUniqueNameSpecificationInterface;
use Doctrine\ORM\EntityManagerInterface;

class OrganizationUniqueNameSpecification implements OrganizationUniqueNameSpecificationInterface
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly OrganizationRepositoryInterface $organizationRepository
    ) {
    }

    public function isSatisfiedBy(string $name, ?string $excludeId = null): bool
    {
        $this->em->getFilters()->disable('softdeleteable');

        $existingOrganization = $this->organizationRepository->findByName($name);

        if (null === $existingOrganization) {
            return true; // Name is unique
        }

        // If we're excluding an ID (for updates), check if the found organization is the same one
        if (null !== $excludeId && $existingOrganization->getId() === $excludeId) {
            return true; // It's the same organization, so name is still unique
        }

        return false; // Name already exists for a different organization
    }
}
