<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Company\Company;
use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use App\Domain\Model\ResultWithMetadata;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Uid\Uuid;

class DoctrineOrganizationRepository extends ServiceEntityRepository implements OrganizationRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Organization::class);
    }

    public function save(Organization $organization): void
    {
        $this->getEntityManager()->persist($organization);
        $this->getEntityManager()->flush();
    }

    public function find($id, $lockMode = null, ?int $lockVersion = null): ?Organization
    {
        return $this->findOneBy(['id' => $id]);
    }

    public function findByName(string $name): ?Organization
    {
        return $this->findOneBy(['name' => $name]);
    }

    public function findByEmail(string $email): ?Organization
    {
        return $this->findOneBy(['email' => $email]);
    }

    public function findByPhone(string $phone): ?Organization
    {
        return $this->findOneBy(['phone' => $phone]);
    }



    /**
     * @return Organization[]
     */
    public function findAll(): array
    {
        return $this->findBy([]);
    }

    /**
     * @param array<string, mixed>                               $filters
     * @param array<array{attribute: string, direction: string}> $sort
     */
    public function search(int $limit, int $offset, array $filters, array $sort): ResultWithMetadata
    {
        $query = $this->getEntityManager()->createQueryBuilder()
            ->select('o')
            ->from(Organization::class, 'o')
            ->where('o.deletedAt IS NULL')
            ->setFirstResult($offset)
            ->setMaxResults($limit);

        $this->setOrganizationFilters($query, $filters);

        $this->setOrganizationSort($query, $sort);

        $paginator = new Paginator($query);

        $items = $paginator->getQuery()->getResult();

        return new ResultWithMetadata($items, $limit, $offset, $paginator->count());
    }

    public function countAll(array $filters = []): int
    {
        $qb = $this->createQueryBuilder('o')
            ->select('COUNT(o.id)')
            ->where('o.deletedAt IS NULL');

        if (isset($filters['name']) && !empty($filters['name'])) {
            $qb->andWhere('o.name LIKE :name')
               ->setParameter('name', '%' . $filters['name'] . '%');
        }

        if (isset($filters['email']) && !empty($filters['email'])) {
            $qb->andWhere('o.email LIKE :email')
               ->setParameter('email', '%' . $filters['email'] . '%');
        }

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    public function remove(Organization $organization): void
    {
        $this->getEntityManager()->remove($organization);
        $this->getEntityManager()->flush();
    }

    /**
     * @param array<string, mixed> $filters
     */
    private function setOrganizationFilters(QueryBuilder $query, array $filters): void
    {
        foreach ($filters as $filterName => $filterValue) {
            switch ($filterName) {
                case 'name':
                    $query
                        ->andWhere($query->expr()->like('o.name', ':name'))
                        ->setParameter('name', '%'.$filterValue.'%');
                    break;
                case 'email':
                    $query
                        ->andWhere($query->expr()->like('o.email', ':email'))
                        ->setParameter('email', '%'.$filterValue.'%');
                    break;
            }
        }
    }

    /**
     * @param array<array{attribute: string, direction: string}> $sort
     */
    private function setOrganizationSort(QueryBuilder $query, array $sort): void
    {
        foreach ($sort as $sortRule) {
            switch ($sortRule['attribute']) {
                case 'createdAt':
                    $query->addOrderBy('o.createdAt', $sortRule['direction']);
                    break;
                case 'name':
                    $query->addOrderBy('o.name', $sortRule['direction']);
                    break;
            }
        }
    }

}
