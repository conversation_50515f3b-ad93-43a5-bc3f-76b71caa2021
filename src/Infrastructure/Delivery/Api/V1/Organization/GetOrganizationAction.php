<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use App\Infrastructure\Http\RequestMapper;
use OpenApi\Attributes as OA;
use App\Application\Query\Organization\Get\GetOrganizationQuery;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Get(
    path: '/tenants/v1/{_locale}/organizations/{id}',
    summary: 'Get Organization',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'id',
            description: 'Organization ID',
            in: 'path',
            required: true,
            schema: new OA\Schema(type: 'string', format: 'uuid')
        ),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_OK,
            description: 'Organization found',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_NOT_FOUND,
            description: 'Organization not found',
            content: new OA\JsonContent(ref: '#/components/schemas/ErrorSchema')
        ),
        new OA\Response(ref: '#/components/responses/InternalServerErrorResponse', response: Response::HTTP_INTERNAL_SERVER_ERROR),
    ]
)]
final class GetOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly QueryBusInterface $queryBus,
        private readonly RequestMapper     $requestMapper,
        private readonly ResponseMapper    $responseMapper
    ) {}

    #[Route('/organizations/{id}', name: 'app_get_organization', methods: ['GET'])]
    public function __invoke(Request $request): JsonResponse
    {
        $query = $this->requestMapper->fromRequest($request, GetOrganizationQuery::class);

        return $this->responseMapper->serializeResponse($this->queryBus->ask($query));
    }
}
