<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Command\Organization\Update\UpdateOrganizationCommand;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Put(
    path: '/tenants/v1/{_locale}/organizations/{id}',
    summary: 'Update Organization',
    security: [['bearerAuth' => []]],
    requestBody: new OA\RequestBody(
        description: 'Organization data',
        required: true,
        content: new OA\JsonContent(
            required: ['name', 'email', 'address'],
            properties: [
                new OA\Property(
                    property: 'name',
                    description: 'Organization name (must be unique)',
                    type: 'string',
                    maxLength: 100,
                    minLength: 1
                ),
                new OA\Property(
                    property: 'email',
                    description: 'Valid email address',
                    type: 'string',
                    format: 'email'
                ),
                new OA\Property(
                    property: 'phone',
                    description: 'Phone number (3-30 characters, international format recommended)',
                    type: 'string',
                    nullable: true,
                    minLength: 3,
                    maxLength: 30,
                    pattern: '^[(]?[+]?[0-9\s()-]*[0-9]{1,}[\s()-]*$'
                ),
                new OA\Property(
                    property: 'address',
                    description: 'Full address as plain text',
                    type: 'string',
                    maxLength: 255
                ),
            ],
            example: [
                'name' => 'ACME Holdings Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Business Ave, New York, NY 10001, US'
            ]
        )
    ),
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'id',
            description: 'Organization ID',
            in: 'path',
            required: true,
            schema: new OA\Schema(type: 'string', format: 'uuid')
        ),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_OK,
            description: 'Organization updated successfully',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_NOT_FOUND,
            description: 'Organization not found',
            content: new OA\JsonContent(ref: '#/components/schemas/ErrorSchema')
        ),
        new OA\Response(
            response: Response::HTTP_BAD_REQUEST,
            description: 'Validation error',
            content: new OA\JsonContent(ref: '#/components/schemas/ErrorSchema')
        ),
        new OA\Response(ref: '#/components/responses/InternalServerErrorResponse', response: Response::HTTP_INTERNAL_SERVER_ERROR),
    ]
)]
final class UpdateOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly RequestMapper $requestMapper,
        private readonly ResponseMapper $responseMapper
    ) {}

    /**
     * @throws \ReflectionException
     */
    #[Route('/organizations/{id}', name: 'app_update_organization', methods: ['PUT'])]
    public function __invoke(Request $request): JsonResponse
    {
        /** @var UpdateOrganizationCommand $command */
        $command = $this->requestMapper->fromRequest($request, UpdateOrganizationCommand::class);

        return $this->responseMapper->serializeResponse($this->commandBus->dispatch($command), Response::HTTP_OK);
    }
}
