<?php

declare(strict_types=1);

namespace App\Domain\Model;

final class ResultWithMetadata
{
    private PaginationData $pagination;

    public function __construct(
        private readonly array $items,
        int $limit,
        int $offset,
        int $totalItemsCount
    ) {
        $this->pagination = new PaginationData($limit, $offset, $totalItemsCount);
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function getPagination(): PaginationData
    {
        return $this->pagination;
    }

    /**
     * @return array{items: array, metadata: array{limit: int, offset: int, total: int}}
     */
    public function toArray(callable $callback): array
    {
        return [
            'items' => array_map($callback, $this->getItems()),
            'metadata' => $this->getPagination()->toArray(),
        ];
    }
}
